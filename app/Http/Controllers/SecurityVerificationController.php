<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Str;
use App\Services\SecurityVerificationService;
use SendGrid;
use SendGrid\Mail\Mail;

class SecurityVerificationController extends Controller
{
    protected $securityService;

    public function __construct(SecurityVerificationService $securityService)
    {
        $this->securityService = $securityService;
    }
    /**
     * Send security verification code to user's email
     */
    public function sendVerificationCode(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated'
                ], 401);
            }

            // Check for rate limiting and attempt limits
            if ($this->securityService->hasExceededAttempts($user->id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Too many verification attempts. Please try again later.'
                ], 429);
            }

            // Generate 6-digit verification code
            $verificationCode = $this->generateToken();

            // Create cache key for this verification session
            $sessionId = Str::uuid();
            $cacheKey = "security_verification_{$user->id}_{$sessionId}";

            // Get configurable cache expiration
            $cacheExpires = config('security.session.cache_expires_minutes', 15);

            // Store verification data in cache
            Cache::put($cacheKey, [
                'user_id' => $user->id,
                'email' => $user->email,
                'code' => $verificationCode,
                'created_at' => now(),
                'verified' => false,
                'attempts' => 0
            ], now()->addMinutes($cacheExpires));

            // Add session to active sessions index for middleware tracking
            $this->securityService->addActiveSession($user->id, $sessionId);

            // Send verification email
            $emailSent = $this->sendSecurityVerificationEmail($user->email, $verificationCode);

            if (!$emailSent) {
                // Clean up cache and session index if email failed
                Cache::forget($cacheKey);
                $this->securityService->removeActiveSession($user->id, $sessionId);
                throw new \Exception('Failed to send verification email');
            }

            \Log::info('Security verification code sent successfully', [
                'user_id' => $user->id,
                'session_id' => $sessionId,
                'email' => $this->maskEmail($user->email)
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Verification code sent to your email',
                'session_id' => $sessionId,
                'masked_email' => $this->maskEmail($user->email)
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to send security verification code', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send verification code'
            ], 500);
        }
    }

    private function generateToken()
    {
        $characters = 'ACDEFGHJKMNPQRTUVWXYZ234679';
        return substr(str_shuffle(str_repeat($characters, 6)), 0, 6);
    }


    /**
     * Verify the security code and set secure cookie
     */
    public function verifyCode(Request $request)
    {
        $request->validate([
            'code' => 'required|string|size:6',
            'session_id' => 'required|string|uuid',
            'next' => 'nullable|string'
        ]);

        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated'
                ], 401);
            }

            // Check for rate limiting
            if ($this->securityService->hasExceededAttempts($user->id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Too many verification attempts. Please try again later.'
                ], 429);
            }

            $cacheKey = "security_verification_{$user->id}_{$request->session_id}";
            $verificationData = Cache::get($cacheKey);

            if (!$verificationData) {
                $this->securityService->recordFailedAttempt($user->id);
                // Clean up session from active sessions index
                $this->securityService->removeActiveSession($user->id, $request->session_id);
                return response()->json([
                    'success' => false,
                    'message' => 'Verification session expired or invalid'
                ], 400);
            }

            // Check verification attempts for this session
            $attempts = $verificationData['attempts'] ?? 0;
            if ($attempts >= 5) {
                $this->securityService->recordFailedAttempt($user->id);
                // Clean up session from active sessions index due to too many attempts
                $this->securityService->removeActiveSession($user->id, $request->session_id);
                return response()->json([
                    'success' => false,
                    'message' => 'Too many failed attempts for this session'
                ], 400);
            }

            if ($verificationData['code'] !== $request->code) {
                // Increment attempts counter
                $verificationData['attempts'] = $attempts + 1;
                Cache::put($cacheKey, $verificationData, now()->addMinutes(config('security.session.cache_expires_minutes', 15)));

                $this->securityService->recordFailedAttempt($user->id);
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid verification code'
                ], 400);
            }

            // Clear failed attempts on successful verification
            $this->securityService->clearFailedAttempts($user->id);

            // Mark as verified
            $verificationData['verified'] = true;
            $verificationData['verified_at'] = now();
            Cache::put($cacheKey, $verificationData, now()->addMinutes(config('security.session.cache_expires_minutes', 15)));

            // Remove session from active sessions index since verification is complete
            $this->securityService->removeActiveSession($user->id, $request->session_id);

            // Record first-time security verification if this is the user's first time
            if (!$user->first_security_verification_at) {
                $user->first_security_verification_at = now();
                $user->save();

                \Log::info('First-time security verification recorded', [
                    'user_id' => $user->id,
                    'verified_at' => $user->first_security_verification_at
                ]);
            }

            // Record first-time security verification if this is the user's first time
            if (!$user->first_security_verification_at) {
                $user->first_security_verification_at = now();
                $user->save();

                \Log::info('First-time security verification recorded', [
                    'user_id' => $user->id,
                    'verified_at' => $user->first_security_verification_at
                ]);
            }

            // Create secure cookie payload
            $cookiePayload = [
                'user_id' => $user->id,
                'verified_at' => now()->toISOString(),
                'session_id' => $request->session_id,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent()
            ];

            // Generate secure cookie value with signing/encryption
            $cookieValue = $this->securityService->generateSecureCookieValue($cookiePayload);

            // Get dynamic cookie configuration
            $cookieConfig = $this->securityService->getCookieConfig();

            // Create secure cookie with dynamic configuration
            $cookie = Cookie::make(
                $cookieConfig['name'],
                $cookieValue,
                $cookieConfig['expires_minutes'],
                $cookieConfig['path'],
                $cookieConfig['domain'],
                $cookieConfig['secure'],
                $cookieConfig['http_only'],
                false, // raw
                $cookieConfig['same_site']
            );

            // Determine redirect URL with proper fallback
            $redirectUrl = $this->determineRedirectUrl($request);

            // Debug logging for redirect investigation
            \Log::info('Security verification redirect debugging', [
                'user_id' => $user->id,
                'session_id' => $request->session_id,
                'next_parameter_raw' => $request->input('next'),
                'next_parameter_exists' => $request->has('next'),
                'all_request_inputs' => $request->all(),
                'request_method' => $request->method(),
                'request_url' => $request->fullUrl(),
                'determined_redirect_url' => $redirectUrl
            ]);

            $response = response()->json([
                'success' => true,
                'message' => 'Security verification successful',
                'redirect_url' => $redirectUrl,
                'cookie_set' => true,
                'cookie_config' => [
                    'name' => $cookieConfig['name'],
                    'expires_in_minutes' => $cookieConfig['expires_minutes'],
                    'check_interval_seconds' => $cookieConfig['check_interval_seconds'],
                    'path' => $cookieConfig['path'],
                    'secure' => $cookieConfig['secure'],
                    'http_only' => $cookieConfig['http_only'],
                    'same_site' => $cookieConfig['same_site'],
                    'domain' => $cookieConfig['domain']
                ]
            ]);

            \Log::info('Security verification successful', [
                'user_id' => $user->id,
                'session_id' => $request->session_id,
                'redirect_url' => $redirectUrl,
                'cookie_domain' => $cookieConfig['domain'],
                'cookie_secure' => $cookieConfig['secure']
            ]);

            return $response->withCookie($cookie);

        } catch (\Exception $e) {
            \Log::error('Security verification failed', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'session_id' => $request->session_id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Verification failed'
            ], 500);
        }
    }

    /**
     * Resend verification code with enhanced rate limiting
     */
    public function resendCode(Request $request)
    {
        $request->validate([
            'session_id' => 'required|string|uuid'
        ]);

        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated'
                ], 401);
            }

            // Check for rate limiting and attempt limits
            if ($this->securityService->hasExceededAttempts($user->id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Too many verification attempts. Please try again later.'
                ], 429);
            }

            // Check rate limiting for resend requests
            $rateLimitSeconds = config('security.session.resend_rate_limit_seconds', 60);
            $rateLimitKey = "security_resend_{$user->id}";

            if (Cache::has($rateLimitKey)) {
                return response()->json([
                    'success' => false,
                    'message' => "Please wait {$rateLimitSeconds} seconds before requesting another code"
                ], 429);
            }

            // Set rate limit
            Cache::put($rateLimitKey, true, now()->addSeconds($rateLimitSeconds));

            $cacheKey = "security_verification_{$user->id}_{$request->session_id}";
            $verificationData = Cache::get($cacheKey);

            if (!$verificationData) {
                return response()->json([
                    'success' => false,
                    'message' => 'Verification session expired'
                ], 400);
            }

            // Generate new code
            $newCode = $this->generateToken();

            // Update cache with new code and reset attempts
            $verificationData['code'] = $newCode;
            $verificationData['created_at'] = now();
            $verificationData['attempts'] = 0; // Reset attempts for new code

            $cacheExpires = config('security.session.cache_expires_minutes', 15);
            Cache::put($cacheKey, $verificationData, now()->addMinutes($cacheExpires));

            // Send new verification email
            $emailSent = $this->sendSecurityVerificationEmail($user->email, $newCode);

            if (!$emailSent) {
                throw new \Exception('Failed to send verification email');
            }

            \Log::info('Security verification code resent successfully', [
                'user_id' => $user->id,
                'session_id' => $request->session_id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'New verification code sent'
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to resend security verification code', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'session_id' => $request->session_id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to resend code'
            ], 500);
        }
    }

    /**
     * Send security verification email with comprehensive error handling
     */
    private function sendSecurityVerificationEmail($email, $code)
    {
        try {
            // Get SendGrid API key
            $apiKey = env('SENDGRID_API_KEY');
            if (!$apiKey) {
                \Log::error('SendGrid API key not configured');
                throw new \Exception('Email service not configured');
            }

            // Get template ID - use verification template from config
            $templateId = env('SENDGRID_VERIFICATION_CHECKUP_TEMPLATE_ID') ?: config('mail.templates.verification_token');
            if (!$templateId) {
                \Log::error('SendGrid template ID not configured');
                throw new \Exception('Email template not configured');
            }

            \Log::info('Attempting to send security verification email', [
                'email' => $this->maskEmail($email),
                'template_id' => $templateId,
                'code_length' => strlen($code)
            ]);

            // Initialize SendGrid
            $sendgrid = new SendGrid($apiKey, ['verify_ssl' => false]);

            // Create email message
            $message = new Mail();
            $message->setFrom(env('MAIL_FROM_ADDRESS', '<EMAIL>'), env('MAIL_FROM_NAME', 'TradeReply'));
            $message->setReplyTo(env('MAIL_TO_REPLY', '<EMAIL>'), 'Support Team');
            $message->setSubject("Security Verification Code");
            $message->addTo($email);
            $message->setTemplateId($templateId);

            // Add dynamic template data
            $message->addDynamicTemplateData('accountVerificationToken', $code);

            \Log::info('Sending email via SendGrid', [
                'to' => $this->maskEmail($email),
                'from' => env('MAIL_FROM_ADDRESS', '<EMAIL>'),
                'template_id' => $templateId
            ]);

            // Send email and check response
            $response = $sendgrid->send($message);

            \Log::info('SendGrid response received', [
                'status_code' => $response->statusCode(),
                'headers' => $response->headers(),
                'body' => $response->body()
            ]);

            // Check if email was sent successfully
            if ($response->statusCode() >= 200 && $response->statusCode() < 300) {
                \Log::info('Security verification email sent successfully', [
                    'email' => $this->maskEmail($email),
                    'status_code' => $response->statusCode()
                ]);
                return true;
            } else {
                \Log::error('SendGrid returned error status', [
                    'status_code' => $response->statusCode(),
                    'body' => $response->body(),
                    'email' => $this->maskEmail($email)
                ]);
                throw new \Exception('Email service returned error: ' . $response->statusCode());
            }

        } catch (\Exception $e) {
            \Log::error('Failed to send security verification email', [
                'error' => $e->getMessage(),
                'email' => $this->maskEmail($email),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Mask email address for display
     * Format: ** + last letter + domain (**<EMAIL>)
     * Matches frontend hashInput() implementation for consistency with signup flow
     */
    private function maskEmail($email)
    {
        if (!$email) {
            return "";
        }

        $parts = explode('@', $email);
        if (count($parts) !== 2) {
            return $email;
        }

        $username = $parts[0];
        $domain = $parts[1];

        if (strlen($username) <= 1) {
            return "**@" . $domain;
        }

        $lastChar = $username[strlen($username) - 1];
        return "**" . $lastChar . '@' . $domain;
    }

    /**
     * Determine the appropriate redirect URL after verification with enhanced debugging
     */
    private function determineRedirectUrl(Request $request): string
    {
        $nextUrl = $request->input('next');
        $referrer = $request->header('Referer');

        \Log::info('Determining redirect URL', [
            'next_parameter' => $nextUrl,
            'referrer_header' => $referrer,
            'all_request_data' => $request->all(),
            'request_headers' => $request->headers->all()
        ]);

        // Priority 1: Check 'next' parameter from request
        if ($nextUrl) {
            \Log::info('Found next parameter', ['next_url' => $nextUrl]);

            // Decode URL if it's encoded
            $decodedUrl = urldecode($nextUrl);
            \Log::info('Decoded next URL', ['decoded_url' => $decodedUrl]);

            // Validate the redirect URL for security
            if ($this->isValidRedirectUrl($decodedUrl)) {
                // Extract path from full URL for frontend compatibility
                $finalUrl = $this->extractPathFromUrl($decodedUrl);
                \Log::info('Next URL is valid, using it', [
                    'original_url' => $decodedUrl,
                    'final_redirect_url' => $finalUrl
                ]);
                return $finalUrl;
            } else {
                \Log::warning('Next URL failed validation', ['invalid_url' => $decodedUrl]);
            }
        }

        // Priority 2: Check for referrer in request headers
        if ($referrer && $this->isValidRedirectUrl($referrer)) {
            \Log::info('Using referrer header', ['referrer' => $referrer]);

            $parsedUrl = parse_url($referrer);
            if (isset($parsedUrl['path'])) {
                $path = $parsedUrl['path'];
                if (isset($parsedUrl['query'])) {
                    $path .= '?' . $parsedUrl['query'];
                }
                if (isset($parsedUrl['fragment'])) {
                    $path .= '#' . $parsedUrl['fragment'];
                }

                \Log::info('Constructed path from referrer', ['constructed_path' => $path]);
                return $path;
            }
        }

        // Priority 3: Default fallback
        \Log::info('Using default fallback redirect', ['default_url' => '/account/overview']);
        return '/account/overview';
    }

    /**
     * Validate that a URL path is a valid secure route
     */
    private function isValidSecureRoute(string $path): bool
    {
        // Decode URL if it's encoded
        $decodedPath = urldecode($path);

        // Extract just the path part (remove query parameters for validation)
        $pathOnly = parse_url($decodedPath, PHP_URL_PATH) ?: $decodedPath;

        // Remove leading slash if present for consistent comparison
        $pathOnly = ltrim($pathOnly, '/');

        // Get secure pages from centralized configuration only
        $securePages = config('security.protected_routes', []);

        foreach ($securePages as $securePage) {
            // Remove leading slash from secure page for comparison
            $securePagePath = ltrim($securePage, '/');

            // Check if the path matches exactly or starts with the secure page path
            if ($pathOnly === $securePagePath || str_starts_with($pathOnly, $securePagePath)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Validate redirect URL for security with enhanced logging
     */
    private function isValidRedirectUrl(string $url): bool
    {
        \Log::info('Validating redirect URL', ['url' => $url]);

        // Don't redirect back to security-check page
        if (str_contains($url, '/security-check')) {
            \Log::info('URL rejected: contains security-check', ['url' => $url]);
            return false;
        }

        // For relative URLs, validate against secure routes
        if (!str_starts_with($url, 'http')) {
            $isValidSecureRoute = $this->isValidSecureRoute($url);
            \Log::info('URL validation result for relative URL', [
                'url' => $url,
                'is_valid_secure_route' => $isValidSecureRoute
            ]);
            return $isValidSecureRoute;
        }

        // For absolute URLs, check if they're from the same domain
        try {
            $parsedUrl = parse_url($url);
            $currentHost = request()->getHost();
            $urlHost = $parsedUrl['host'] ?? null;

            \Log::info('Validating absolute URL', [
                'url' => $url,
                'parsed_host' => $urlHost,
                'current_host' => $currentHost
            ]);

            if ($urlHost) {
                // Handle localhost with different ports (frontend vs backend)
                $isLocalhost = in_array($urlHost, ['localhost', '127.0.0.1']) &&
                              in_array($currentHost, ['localhost', '127.0.0.1']);

                // Allow same domain or localhost with different ports, but also validate the path
                if ($urlHost === $currentHost || $isLocalhost) {
                    // Extract path from absolute URL and validate it's a secure route
                    $urlPath = $parsedUrl['path'] ?? '/';
                    if (isset($parsedUrl['query'])) {
                        $urlPath .= '?' . $parsedUrl['query'];
                    }

                    $isValidSecureRoute = $this->isValidSecureRoute($urlPath);
                    \Log::info('Absolute URL domain validation passed, checking secure route', [
                        'url' => $url,
                        'extracted_path' => $urlPath,
                        'is_valid_secure_route' => $isValidSecureRoute
                    ]);
                    return $isValidSecureRoute;
                }

                // Check if both are localhost-like domains
                if (str_contains($urlHost, 'localhost') && str_contains($currentHost, 'localhost')) {
                    // Extract path from absolute URL and validate it's a secure route
                    $urlPath = $parsedUrl['path'] ?? '/';
                    if (isset($parsedUrl['query'])) {
                        $urlPath .= '?' . $parsedUrl['query'];
                    }

                    $isValidSecureRoute = $this->isValidSecureRoute($urlPath);
                    \Log::info('Localhost URL validation, checking secure route', [
                        'url' => $url,
                        'extracted_path' => $urlPath,
                        'is_valid_secure_route' => $isValidSecureRoute
                    ]);
                    return $isValidSecureRoute;
                }

                \Log::info('URL rejected: different host', [
                    'url' => $url,
                    'parsed_host' => $urlHost,
                    'current_host' => $currentHost
                ]);
                return false;
            }

            \Log::info('URL accepted: no host specified', ['url' => $url]);
            return true;
        } catch (\Exception $e) {
            \Log::error('URL validation failed with exception', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Extract path from URL for frontend compatibility
     */
    private function extractPathFromUrl(string $url): string
    {
        // If it's already a relative URL, return as is
        if (!str_starts_with($url, 'http')) {
            return $url;
        }

        try {
            $parsedUrl = parse_url($url);
            $path = $parsedUrl['path'] ?? '/';

            // Add query string if present
            if (isset($parsedUrl['query'])) {
                $path .= '?' . $parsedUrl['query'];
            }

            // Add fragment if present
            if (isset($parsedUrl['fragment'])) {
                $path .= '#' . $parsedUrl['fragment'];
            }

            \Log::info('Extracted path from URL', [
                'original_url' => $url,
                'extracted_path' => $path
            ]);

            return $path;
        } catch (\Exception $e) {
            \Log::warning('Failed to extract path from URL', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            return $url; // Return original if parsing fails
        }
    }

    /**
     * Get security configuration for frontend
     */
    public function getSecurityConfig(Request $request)
    {
        try {
            $cookieConfig = $this->securityService->getCookieConfig();

            return response()->json([
                'success' => true,
                'config' => [
                    'cookie_name' => $cookieConfig['name'],
                    'expires_in_minutes' => $cookieConfig['expires_minutes'],
                    'check_interval_seconds' => $cookieConfig['check_interval_seconds'],
                    'check_interval_milliseconds' => $cookieConfig['check_interval_seconds'] * 1000,
                    // Add protected routes from centralized config
                    'protected_routes' => config('security.protected_routes', []),
                    // Add referrer control configuration
                    'referrer_control' => [
                        'enabled' => config('security.referrer_control.enabled', true),
                        'valid_referrer_prefixes' => config('security.referrer_control.valid_referrer_prefixes', []),
                        'invalid_referrer_paths' => config('security.referrer_control.invalid_referrer_paths', []),
                        'fallback_urls' => config('security.referrer_control.fallback_urls', []),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to get security config', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get security configuration'
            ], 500);
        }
    }

    /**
     * Check if the current request has a valid security cookie
     */
    public function checkSecurityStatus(Request $request)
    {
        try {
            $cookieConfig = $this->securityService->getCookieConfig();
            $cookieValue = $request->cookie($cookieConfig['name']);

            if (!$cookieValue) {
                return response()->json([
                    'success' => false,
                    'verified' => false,
                    'message' => 'No security verification cookie found'
                ]);
            }

            $payload = $this->securityService->validateSecureCookieValue($cookieValue);

            if (!$payload) {
                return response()->json([
                    'success' => false,
                    'verified' => false,
                    'message' => 'Invalid or expired security verification'
                ]);
            }

            return response()->json([
                'success' => true,
                'verified' => true,
                'verified_at' => $payload['verified_at'] ?? null,
                'expires_in_minutes' => config('security.cookie.expires_minutes', 1)
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to check security status', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'verified' => false,
                'message' => 'Failed to check security status'
            ], 500);
        }
    }

    /**
     * Test email functionality (for debugging purposes)
     */
    public function testEmail(Request $request)
    {
        $request->validate([
            'email' => 'required|email'
        ]);

        try {
            $testCode = '123456';
            $emailSent = $this->sendSecurityVerificationEmail($request->email, $testCode);

            return response()->json([
                'success' => $emailSent,
                'message' => $emailSent ? 'Test email sent successfully' : 'Failed to send test email',
                'email' => $this->maskEmail($request->email),
                'test_code' => $testCode
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Email test failed: ' . $e->getMessage(),
                'email' => $this->maskEmail($request->email)
            ], 500);
        }
    }

    /**
     * Debug redirect logic (for testing purposes)
     */
    public function debugRedirect(Request $request)
    {
        try {
            $redirectUrl = $this->determineRedirectUrl($request);

            return response()->json([
                'success' => true,
                'redirect_url' => $redirectUrl,
                'request_data' => $request->all(),
                'headers' => $request->headers->all(),
                'next_parameter' => $request->input('next'),
                'referrer' => $request->header('Referer')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Debug failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
