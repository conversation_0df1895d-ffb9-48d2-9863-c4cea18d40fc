import Home from "@/Components/common/Home";
import HomeLayout from "@/Layouts/HomeLayout";
import "../css/Home/home.scss";
import MetaHead from "@/Seo/Meta/MetaHead";

export default async function Welcome() {
  let featuredArticle = null;

  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/featured-resource`, {
      cache: 'no-store',
    });
    if (res.ok) {
      const json = await res.json();
      featuredArticle = json?.data;
    } else {
      console.error("Failed to fetch featured resource:", res.status);
    }
  } catch (err) {
    console.error("Error while fetching featured resource:", err);
  }

  const metaArray = {
    title: "TradeReply: Advanced Trading Tools & Strategy Optimization",
    description:
      "Optimize your trades with TradeReply.com. Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success.",
    canonical_link: "https://www.tradereply.com/",
    og_title: "TradeReply: Advanced Trading Tools & Strategy Optimization",
    og_site_name: "TradeReply",
    og_description:
      "Optimize your trades with TradeReply.com. Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success.",
    twitter_title: "TradeReply: Advanced Trading Tools & Strategy Optimization",
    twitter_description:
      "Optimize your trades with TradeReply.com. Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success.",
  };

  return (
    <HomeLayout>
      <MetaHead props={metaArray} />
      <Home featuredArticle={featuredArticle} />
    </HomeLayout>
  );
}
