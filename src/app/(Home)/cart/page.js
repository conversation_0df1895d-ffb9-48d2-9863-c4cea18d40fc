"use client";
import React from 'react'
import HomeLayout from "@/Layouts/HomeLayout";
import MetaHead from '@/Seo/Meta/MetaHead';
import { Container } from "react-bootstrap";
import { Row, Col } from "react-bootstrap";
import "../../../css/Home/Cart.scss";
import { DeviceMobileSpeaker, BlackErrorCircle, ViewCartBaseBlue, CheckoutCardGray, AccessGray } from "@/assets/svgIcons/SvgIcon";
import CommonButton from '@/Components/UI/CommonButton';
import NavLink from '@/Components/UI/NavLink';
import { useEffect, useState } from "react";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";

export default function page() {
    const [loginToken, setLoginToken] = useState(null);
    const [orders, setOrders] = useState([]);

    useEffect(() => {
        const tokens = Cookies.get("authToken");
        setLoginToken(tokens || null);
    }, []);

    useEffect(() => {
    const fetchOrders = async () => {
        if (!loginToken) return;

        try {
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/orders`, {
                headers: {
                    Authorization: `Bearer ${loginToken}`,
                    Accept: 'application/json',
                },
            });
            const resJson = await response.json();
            setOrders(resJson?.data || []);
        } catch (error) {
            console.error("Failed to fetch orders", error);
        }
    };
        fetchOrders();
    }, [loginToken]);

    const handleRemove = async (orderId) => {
        if (!loginToken) return;

        try {
            await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/orders/${orderId}`, {
                method: "DELETE",
                headers: {
                    Authorization: `Bearer ${loginToken}`,
                    Accept: "application/json",
                },
            });

            setOrders(prev => prev.filter(order => order.id !== orderId));
        } catch (error) {
            console.error("Failed to delete order", error);
        }
    };

    const router = useRouter();
    const cartItems = orders.filter(order => order.status === 'pending');

    const subtotal = cartItems.reduce((total, item) => {
        if(item?.is_free_subscription) {
            return 0;
        }
        const numericPrice = parseFloat(item.price?.replace('$', '') || 0);
        return total + numericPrice;
    }, 0);

    const formattedSubtotal = subtotal.toFixed(2);

    const nextBillingDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric'
    });

    const metaArray = {
        noindex: true,
        title: "Shopping Cart | Review Your Items | TradeReply",
        description: "Review the items in your TradeReply Marketplace cart before proceeding to checkout. Make sure you have everything you need for successful trading.",
        canonical_link: "https://www.tradereply.com/cart",
        og_site_name: "TradeReply",
        og_title: "Shopping Cart | Review Your Items | TradeReply Marketplace",
        og_description: "Review the items in your TradeReply Marketplace cart before proceeding to checkout. Make sure you have everything you need for successful trading.",
        twitter_title: "Shopping Cart | Review Your Items | TradeReply Marketplace",
        twitter_description: "Review the items in your TradeReply Marketplace cart before proceeding to checkout. Make sure you have everything you need for successful trading."
    };

    return (
        <>
            <HomeLayout>
                <MetaHead props={metaArray} />
                <div className="cart">
                    <Container>
                        <div className='cartContainer'>
                            <div className={`${!loginToken ? "d-none" : ""}`}>
                                <Row>
                                    <Col sm={12} lg={8} className='order-lg-1 order-2'>
                                        <p className='cartContainer_title mt-3 mt-lg-0'>Shopping Cart</p>
                                        <div className='cartContainer_itemsbox'>
                                            <div className='cartContainer_itemsbox_title'>
                                                <p>Your items ({cartItems.length})</p>
                                                <p>USD</p>
                                            </div>
                                            <div className='cartContainer_itemsbox_inner'>
                                                {cartItems?.map((item) => {
                                                    const title = item?.plan?.title || item?.product?.title || "Untitled";
                                                    const billingType = item?.billing_type;
                                                    const isSubscription = item?.order_type === "subscription";

                                                    const formattedTitle = isSubscription
                                                        ? `TradeReply - ${title} Plan (${billingType === 'monthly' ? 'Monthly' : 'Annual'})`
                                                        : title;

                                                    const accessDuration = isSubscription
                                                        ? billingType === 'monthly'
                                                        ? 'Renews every 30 days'
                                                        : 'Renews every 12 months'
                                                        : item?.duration || 'One-time access';

                                                    return (
                                                        <div key={item.id} className="cartContainer_itemsbox_inner_box">
                                                            <div className="d-flex gap-3">
                                                                <img
                                                                    className="itemImg"
                                                                    src={
                                                                        item?.order_type === 'subscription'
                                                                            ? "https://cdn.tradereply.com/dev/site-assets/tradereply-square-logo-black.svg"
                                                                            : item?.product?.image || "/default.png"
                                                                        }
                                                                    alt="Cart Image"
                                                                />
                                                                <div>
                                                                    <p className="itemName">{formattedTitle}</p>
                                                                    <p className="itemFormat">
                                                                        Format: <span>{isSubscription ? "Digital Subscription" : "Digital Product"}</span>
                                                                    </p>
                                                                    <p className="itemDuration">
                                                                        Access Duration: <span>{accessDuration}</span>
                                                                    </p>
                                                                    {isSubscription && (
                                                                        <div className="itemLicense">
                                                                            <DeviceMobileSpeaker />
                                                                            <p>{item?.is_free_subscription ? 'This is a 30-day free trial' : 'Paid plan subscription'}</p>
                                                                            <BlackErrorCircle />
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            </div>
                                                            <div className="cartContainer_itemsbox_inner_box_right">
                                                                 {isSubscription && item?.is_free_subscription ? (
                                                                    <p className="mb-0 order-md-1 order-2 text-end small">
                                                                        <strong>Today's Charge:</strong> $0.00<br />
                                                                        30-day trial starts now.<br />
                                                                        Billed <strong>${item.price}</strong> on <strong>{nextBillingDate}</strong>.
                                                                    </p>
                                                                ) : (
                                                                    <p className="mb-0 order-md-1 order-2">${item?.price}</p>
                                                                )}
                                                                <button
                                                                    className="mb-0 order-md-2 order-1"
                                                                    onClick={() => handleRemove(item.id)}
                                                                >
                                                                    Remove
                                                                </button>
                                                            </div>
                                                        </div>
                                                    );
                                            })}
                                            </div>
                                        </div>
                                    </Col>
                                    <Col sm={12} lg={4} className='order-lg-2 order-1'>
                                        <div className='cartContainer_itemsbox_right'>
                                            <div className='cartContainer_itemsbox_right_top'>
                                                <div className='d-flex flex-column align-items-center'>
                                                    <div className='cart_steps baseblue_border'>
                                                        <ViewCartBaseBlue />
                                                    </div>
                                                    <p className='blue_text'>View Cart</p>
                                                </div>
                                                <div className='border-gray col'></div>
                                                <div className='d-flex flex-column align-items-center'>
                                                    <div className='cart_steps darkgray_border'>
                                                        <CheckoutCardGray />
                                                    </div>
                                                    <p className='darkgrey_text'>Checkout </p>
                                                </div>
                                                <div className='border-gray col'></div>
                                                <div className='d-flex flex-column align-items-center'>
                                                    <div className='cart_steps darkgray_border'>
                                                        <AccessGray />
                                                    </div>
                                                    <p className='darkgrey_text'>Access</p>
                                                </div>
                                            </div>
                                            <div className='cartContainer_itemsbox_right_inner'>
                                                <div className='orderSummary'>
                                                    <p>Order Summary</p>
                                                    <p>USD</p>
                                                </div>
                                                <div className='subtotal'>
                                                    <p>Subtotal:</p>
                                                    <span>${formattedSubtotal}</span>
                                                </div>
                                                <div className='tax'>
                                                    <p>Tax:</p>
                                                    <span>$0</span>
                                                </div>
                                                <div className='orderTotal'>
                                                    <p>Order Total:</p>
                                                    <span>${formattedSubtotal}</span>
                                                </div>
                                            </div>
                                            <div className='d-none d-lg-block'>
                                                <div className='my-4'>
                                                    <CommonButton
                                                        title={loginToken ? "Checkout" : "Sign In and Checkout"}
                                                        fluid
                                                        disabled={cartItems.length === 0}
                                                        style={cartItems.length === 0 ? { opacity: 0.6, cursor: "not-allowed" } : {}}
                                                        onClick={() => {
                                                            if (cartItems.length === 0) return;
                                                            if (loginToken) {
                                                                router.push("/checkout");
                                                            } else {
                                                                router.push("/login");
                                                            }
                                                        }}
                                                    />
                                                </div>
                                                <div className='text-center'>
                                                    <NavLink href="#">
                                                        Continue Shopping
                                                    </NavLink>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                        </div>
                                    </Col>
                                </Row>
                                <div className='d-lg-none d-block'>
                                    <div className='my-4'>
                                        <CommonButton
                                            title="Sign In and Checkout"
                                            fluid
                                        />
                                    </div>
                                    <div className='text-center'>
                                        <NavLink href="#">
                                            Continue Shopping
                                        </NavLink>
                                    </div>
                                </div>
                            </div>
                            <div className={`cartLogOut ${loginToken ? "d-none" : ""}`}>
                                <img className='cartLogOut_image' src="https://cdn.tradereply.com/dev/site-assets/tradereply-learn-trading-strategies.png" alt="Tradereply Cart Image" />
                                <div className='cartLogOut_text'>
                                    <p className='heading'>Your TradeReply Cart is empty</p>
                                    <NavLink href="/marketplace">
                                        <p className='subHeading mt-2'>Discover trading courses, eBooks and more from fellow traders in our marketplace.</p>
                                    </NavLink>
                                    <div className='mt-3 d-flex gap-3'>
                                        <NavLink href="/login">
                                            <CommonButton
                                                title="Sign In to your account"
                                                fluid
                                            />
                                        </NavLink>
                                        <NavLink href="/signup">
                                            <CommonButton
                                                title="Sign up now"
                                                whiteBtn
                                            />
                                        </NavLink>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Container>
                </div>
            </HomeLayout>
        </>
    )
}
