"use client"
import { Col, Container, Row } from "react-bootstrap";
import CommonButton from "@/Components/UI/CommonButton";
import Link from "next/link";
import { CheckIcon, RedCrossIcon } from "@/assets/svgIcons/SvgIcon";
import { useRouter } from "next/navigation";
import Switch from "@/Components/UI/Switch";
import FaqCard from "@/Components/common/Home/FaqCard";
import HomeLayout from "@/Layouts/HomeLayout";
import "@/css/Home/Pricing.scss";
import { useEffect, useState } from "react";
import MetaHead from "@/Seo/Meta/MetaHead";
import { usePathname, useSearchParams } from "next/navigation";
import Cookies from "js-cookie";
import Loader from "@/Components/common/Loader";
import { loadStripe } from '@stripe/stripe-js';
import { get, post } from "@/utils/apiUtils";
import {
  SolidInfoIcon,
  RightSolidArrowIcon,
} from "@/assets/svgIcons/SvgIcon";
import CommonTooltip from "@/Components/UI/CommonTooltip";

export default function PricingClient({ plans, billingPlans, defaultIsMonthly, activeSubscription, authToken }) {
    const [loginToken, setLoginToken] = useState(authToken || null);
    const router = useRouter();
    const [isMonthly, setIsMonthly] = useState(defaultIsMonthly);
    const [isFree, setisFree] = useState(activeSubscription?.plan?.billing_type === "free");
    const [isTrial, setisTrial] = useState(activeSubscription?.is_trial === true);
    const [isAlreadyTrial, setisAlreadyTrial] = useState(false);
    const [currentPlanId, setCurrentPlanId] = useState(activeSubscription?.plan_id || null);
    const [isLoggedIn, setIsLoggedIn] = useState(!!authToken);
    const [filteredPlans, setFilteredPlans] = useState(billingPlans);
    const [isLoading, setIsLoading] = useState(false);

    const pathname = usePathname();
    const searchParams = useSearchParams();

    const handleSwitchplan = () => {
        const newIsMonthly = !isMonthly;
        setIsMonthly(newIsMonthly);

        const filteredPlans = plans?.filter(plan => 
            plan?.billing_type === (newIsMonthly ? "monthly" : "yearly") ||
            plan?.billing_type === "free"
        );

        setFilteredPlans(filteredPlans);
    };

    const handlStripeSubscibe = async (planId, isTrial) => {
        try {
            const user = JSON.parse(localStorage.getItem("user"));
            const userId = user?.id || null;

            const query = new URLSearchParams({
                plan_id: planId,
                is_trial: isTrial ? '1' : '0',
                user_id: userId,
            });

            await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/create/subscription/order?${query.toString()}`, {
                method: 'GET',
                credentials: 'include',
            });

            if (!loginToken) {
                if (isTrial) {
                    Cookies.set("click_paid_button", "true");
                }
                router.push("/signup");
            } else {
                router.push("/cart");
            }
        } catch (err) {
            console.error("Error during subscription request:", err);
        }
    };

    const handleDowngrade = async (planId) => {
        if(!loginToken) {
            router.push("/signup")
            return;
        }

        try {
            await post("/create/subscription/order", {
                plan_id: planId,
                isDowngrade: true
            });
            router.push("/cart")
        } catch (err) {
            console.error("Error saving field:", err);
        }
    };

    const handleDowngradeToFreePlan = async () => {
        try {
            const response = await post("/assign/free/plan");
            fetchActiveSubscription();
        } catch (err) {
            console.error("Error downgrading:", err);
            alert("Failed to downgrade.");
        }
    };

    const metaArray = {
        title: "TradeReply Pricing Plans | Choose Your Trading Tools",
        description: "Discover flexible pricing plans at TradeReply.com. Choose the perfect plan to access advanced trading tools, real-time analytics, and strategy optimization.",
        canonical_link: "https://www.tradereply.com/pricing",
        og_site_name: "TradeReply",
        og_title: "TradeReply Pricing Plans | Choose Your Trading Tools",
        og_description: "Discover flexible pricing plans at TradeReply.com. Choose the perfect plan to access advanced trading tools, real-time analytics, and strategy optimization.",
        twitter_title: "TradeReply Pricing Plans | Choose Your Trading Tools",
        twitter_description: "Discover flexible pricing plans at TradeReply.com. Choose the perfect plan to access advanced trading tools, real-time analytics, and strategy optimization.",
    };
    return (
        <HomeLayout>
            <MetaHead props={metaArray} />
            <div className="pricing">
                <section className="pricing_banner">
                    <Container>
                        <Row className="gx-xl-5 align-items-center justify-content-center">
                            <Col md={isFree ? 12 : 7} xs={12} xl={isFree ? 12 : 8}>
                                <div className="pricing_banner_content text-center">
                                    <h1>
                                        {isLoggedIn
                                            ? currentPlanId !== null
                                                ? "Manage your plan or explore additional features"
                                                : "Try any of our plans, free for 30 days"
                                            : "Join Free, Upgrade Anytime"}
                                    </h1>

                                    <p>
                                        {(() => {
                                            const currentPlanName = plans?.find(p => p?.id === currentPlanId)?.title ?? null;;
                                            if (!isLoggedIn && currentPlanId === null) {
                                                return "Enjoy powerful insights with our free analytics suite. Upgrade anytime for additional premium features.";
                                            }

                                            switch (currentPlanName) {
                                                case "Essential":
                                                    return "You’re currently on the Essential plan. Unlock more advanced tools by upgrading to Plus or Premium.";
                                                case "Plus":
                                                    return "You’re currently on the Plus plan. Want even deeper insights and automation? Upgrade to Premium anytime.";
                                                case "Premium":
                                                    return "You’re on our most advanced plan — Premium. Looking to downgrade or adjust your billing preferences?";
                                                default:
                                                    return "";
                                            }
                                        })()}
                                    </p>
                                </div>
                            </Col>
                            {!isLoggedIn && (
                                <Col md={5} xs={12} xl={4}>
                                    <div className="pricing_banner_forever">
                                        <h3>$0 forever</h3>
                                        <div>
                                            <CommonButton
                                                onClick={() => {
                                                    sessionStorage.setItem("pricing", "free");
                                                    sessionStorage.setItem("trial", "false");
                                                    router.push("/signup");
                                                }}
                                                title="Join Free"
                                                className="gradient-btn my-3 my-md-4"
                                            />
                                        </div>
                                        <h4>No Credit Card Required</h4>
                                    </div>
                                </Col>
                            )}
                        </Row>
                    </Container>
                </section>
                <section className="pricing_table">
                    <Container>
                        <div className="pricing_table_switch d-flex align-items-center justify-content-center">
                            <p>Monthly</p>
                            <Switch
                                checked={Boolean(!isMonthly)}
                                onChange={handleSwitchplan}
                            />
                            <p>Annually</p>
                        </div>
                        <Row className="gx-0 gy-4">
                            {filteredPlans
                                .map((item, index) => {
                                const thisPlanId = item?.id;
                                const thisPlanOrder = item?.order;
                                const thisPlanBillingType = item?.billing_type;
                                const currentPlanObj = plans.find(plan => plan.id === currentPlanId);
                                const currentPlanOrder = currentPlanObj?.order || 0;
                                const currentPlanBillingType = currentPlanObj?.billing_type;

                                let buttonTitle = "";
                                let buttonClass = "";
                                let showButton = true;
                                let actionType = "subscribe";

                                if (!currentPlanId && !isAlreadyTrial) {
                                    buttonTitle = "Try Free for 30 Days";
                                    buttonClass = "green-btn free_for";
                                } else if (isTrial) {
                                    if (thisPlanId === currentPlanId) {
                                        buttonTitle = "Current Plan Change Billing Cycle";
                                        buttonClass = "gray-btn";
                                    } else {
                                        if(thisPlanBillingType === 'free') {
                                            buttonTitle = "Downgrade To Free";
                                            buttonClass = "yellow-btn";
                                            actionType = "handleDowngradeToFreePlan";
                                        } else {
                                            buttonTitle = "Upgrade";
                                            buttonClass = "green-btn";
                                        }
                                    }
                                } else if (isFree) {
                                    if (thisPlanId === currentPlanId) {
                                        buttonTitle = "Current Plan Change Billing Cycle";
                                        buttonClass = "gray-btn";
                                    } else {
                                        buttonTitle = "Try Free for 30 Days";
                                        buttonClass = "green-btn free_for";
                                    }
                                } else {
                                    if (thisPlanId === currentPlanId) {
                                        buttonTitle = "Current Plan Change Billing Cycle";
                                        buttonClass = "gray-btn";
                                    } else if (thisPlanOrder > currentPlanOrder) {
                                        buttonTitle = "Upgrade";
                                        buttonClass = "green-btn";
                                    } else if (thisPlanOrder < currentPlanOrder) {
                                        buttonTitle = "Downgrade";
                                        buttonClass = "yellow-btn";
                                        actionType = "downgrade";
                                    } else {
                                        if(thisPlanBillingType === 'free') {
                                            buttonTitle = "Downgrade To Free";
                                            buttonClass = "yellow-btn";
                                            actionType = "handleDowngradeToFreePlan";
                                        } else {
                                            buttonTitle = "Upgrade";
                                            buttonClass = "green-btn";
                                        }
                                    }
                                }
                                return (
                                    <Col lg={4} xs={12} key={index} className="pricing_table_col d-flex">
                                        <div className="pricing_table_box w-100">
                                            <div className="pricing_table_box_heading">
                                                <h3>{item?.title}</h3>
                                                <h2>
                                                    ${item?.price}
                                                    {thisPlanBillingType !== 'free' && (
                                                        <span> /{!isMonthly ? 'year' : 'month'} </span>
                                                    )}
                                                </h2>
                                                {!isMonthly && thisPlanBillingType !== 'free'  && (
                                                    <>
                                                        <p>{item?.billed_description || ''}</p>
                                                        <p>({item?.discount || ''})</p>
                                                    </>
                                                )}
                                                {showButton && (
                                                    <CommonButton
                                                        onClick={() => {
                                                            if (actionType === "downgrade") {
                                                                handleDowngrade(item?.id);
                                                            } else if(actionType === 'handleDowngradeToFreePlan') {
                                                                handleDowngradeToFreePlan();
                                                            } else {
                                                                handlStripeSubscibe(item?.id, currentPlanId == null);
                                                            }
                                                        }}
                                                        title={buttonTitle}
                                                        disabled={buttonTitle.includes("Current Plan")}
                                                        className={`btn-style ${buttonClass}`}
                                                        style={buttonTitle.includes("Current Plan") ? { cursor: "not-allowed", opacity: 0.6 } : {}}
                                                    />
                                                )}
                                                {thisPlanBillingType !== 'free' && !currentPlanId && (
                                                    <p>
                                                        No Trial Needed?{" "}
                                                        <Link
                                                            href="#"
                                                            onClick={() => {
                                                                handlStripeSubscibe(item?.id, false);
                                                            }}
                                                            className="text-blue-500"
                                                        >
                                                            Pay Now
                                                        </Link>
                                                    </p>
                                                )}
                                            </div>
                                            <ul>
                                               {item?.rules?.map((rule, i) => (
                                                <li className="d-flex gap-2 align-items-center" key={i}>
                                                    {rule.is_allowed ? (
                                                        <CheckIcon width="20" height="20" />
                                                    ) : (
                                                        <RedCrossIcon />
                                                    )}
                                                    <span className="d-flex align-items-center gap-2">
                                                        {rule.description}
                                                        {rule.key === 'trade_history' && thisPlanBillingType === 'free' && (
                                                            <CommonTooltip
                                                                className="d-flex align-items-center"
                                                                content="Older trades are securely stored. Upgrade to access your full history."
                                                                position="top-left"
                                                            >
                                                                <SolidInfoIcon width="16" height="16" />
                                                            </CommonTooltip>
                                                        )}
                                                    </span>
                                                </li>
                                            ))}
                                            </ul>
                                        </div>
                                    </Col>
                                );
                            })}
                        </Row>
                    </Container>
                </section>
                <div className="py-50">
                    <Container>
                        <FaqCard isPricing={true} />
                    </Container>
                </div>
            </div>
        </HomeLayout>
    );
};