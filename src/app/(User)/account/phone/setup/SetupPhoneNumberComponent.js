'use client';
import { Col, Row } from "react-bootstrap";
import React, { useState, useEffect } from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import TextInput from "@/Components/UI/TextInput";
import StatusIndicator from "@/Components/UI/StatusIndicator";
import { post, get } from "@/utils/apiUtils";
import { useRouter, useSearchParams } from "next/navigation";
import { useSelector } from 'react-redux';
import { useSecurityCookieMonitor } from "@/Hooks/useSecurityCookieMonitor";
import "@/css/account/AccountDetails.scss";

export default function SetupPhoneNumberComponent() {
    const [phoneNumber, setPhoneNumber] = useState('');
    const [originalPhoneNumber, setOriginalPhoneNumber] = useState('');
    const [smsUpdates, setSmsUpdates] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [isLoadingUserData, setIsLoadingUserData] = useState(true);
    const [saveStatus, setSaveStatus] = useState(null); // 'loading', 'success', 'error', null
    const [error, setError] = useState(null);

    // Validation state
    const [validationError, setValidationError] = useState(null);
    const [isTouched, setIsTouched] = useState(false);

    const router = useRouter();
    const searchParams = useSearchParams();
    const reduxUser = useSelector((state) => state?.auth?.user || null);

    // Security cookie monitoring
    useSecurityCookieMonitor();

    // Meta data for SEO
    const metaArray = [
        { name: "title", content: "Setup Phone Number - TradeReply" },
        { name: "description", content: "Add your phone number to receive important notifications and updates." },
    ];

    // Phone number validation function
    const validatePhoneNumber = (phone) => {
        // Required field validation
        if (!phone || !phone.trim()) {
            return "Phone number is required";
        }

        const trimmedPhone = phone.trim();

        // Format validation - must start with +
        if (!trimmedPhone.startsWith('+')) {
            return "Phone number must start with + followed by country code (e.g., +1 for US, +44 for UK)";
        }

        // Length validation (8-17 characters including +)
        if (trimmedPhone.length < 8) {
            return "Phone number must be between 8-17 characters";
        }
        if (trimmedPhone.length > 17) {
            return "Phone number must be between 8-17 characters";
        }

        // Character validation - only allow digits, spaces, hyphens, parentheses, and +
        const validCharacters = /^[+\d\s\-()]+$/;
        if (!validCharacters.test(trimmedPhone)) {
            return "Phone number can only contain digits, spaces, hyphens, parentheses, and +";
        }

        // Additional format validation - ensure there are digits after the +
        const digitsAfterPlus = trimmedPhone.slice(1).replace(/[\s\-()]/g, '');
        if (digitsAfterPlus.length < 7) {
            return "Phone number must start with + followed by country code (e.g., +1 for US, +44 for UK)";
        }

        return null; // No validation errors
    };

    // Fetch user data to pre-populate phone number
    const fetchUserData = async () => {
        try {
            setIsLoadingUserData(true);
            const response = await get('/account');

            if (response.success && response.data) {
                const existingPhone = response.data.phone_number || '';
                setPhoneNumber(existingPhone);
                setOriginalPhoneNumber(existingPhone);
            }
        } catch (error) {
            console.error('Error fetching user data:', error);
            // Fallback to Redux user data
            if (reduxUser?.phone_number) {
                setPhoneNumber(reduxUser.phone_number);
                setOriginalPhoneNumber(reduxUser.phone_number);
            }
        } finally {
            setIsLoadingUserData(false);
        }
    };

    useEffect(() => {
        fetchUserData();
    }, []);

    // Validate phone number when it changes (including initial load)
    useEffect(() => {
        if (phoneNumber && !isTouched) {
            // For pre-populated phone numbers, don't show validation errors initially
            const error = validatePhoneNumber(phoneNumber);
            setValidationError(error);
        }
    }, [phoneNumber, isTouched]);

    const handlePhoneChange = (e) => {
        let value = e.target.value;

        // Only allow digits, spaces, hyphens, parentheses, and plus
        const filteredValue = value.replace(/[^+\d\s\-()]/g, '');

        setPhoneNumber(filteredValue);
        setIsTouched(true);

        const error = validatePhoneNumber(filteredValue);
        setValidationError(error);

        if (saveStatus) {
            setSaveStatus(null);
            setError(null);
        }
    };


    const isUpdating = originalPhoneNumber.length > 0;

    // Determine redirect URL based on referrer or URL parameters
    const getRedirectUrl = () => {
        // Check for explicit redirect parameter in URL
        const redirectParam = searchParams.get('redirect');
        if (redirectParam) {
            try {
                // Decode the redirect parameter
                return decodeURIComponent(redirectParam);
            } catch (e) {
                console.warn('Failed to decode redirect parameter:', redirectParam);
            }
        }

        // Check for referrer from URL parameter (for programmatic navigation)
        const fromParam = searchParams.get('from');
        if (fromParam) {
            return fromParam;
        }

        // Check for next parameter (common pattern)
        const nextParam = searchParams.get('next');
        if (nextParam) {
            try {
                return decodeURIComponent(nextParam);
            } catch (e) {
                console.warn('Failed to decode next parameter:', nextParam);
            }
        }

        // Check document referrer as fallback
        if (typeof window !== 'undefined' && document.referrer) {
            try {
                const referrerUrl = new URL(document.referrer);
                // Only use referrer if it's from the same origin
                if (referrerUrl.origin === window.location.origin) {
                    return referrerUrl.pathname + referrerUrl.search;
                }
            } catch (e) {
                console.warn('Failed to parse document referrer:', document.referrer);
            }
        }

        // Default fallback
        return '/account/details';
    };



    const handleSave = async () => {
        // Mark field as touched for validation display
        setIsTouched(true);

        // Validate phone number before submission
        const validationErr = validatePhoneNumber(phoneNumber);
        if (validationErr) {
            setValidationError(validationErr);
            return;
        }

        try {
            setIsLoading(true);
            setSaveStatus('loading');
            setError(null);
            setValidationError(null); // Clear any validation errors

            const response = await post('/phone/setup', {
                phone_number: phoneNumber,
            });

            if (response.success) {
                setSaveStatus('success');

                // Get the appropriate redirect URL
                const redirectUrl = getRedirectUrl();
                console.log('Phone setup success - redirecting to:', redirectUrl);

                // Redirect back to the original page after 2 seconds
                setTimeout(() => {
                    router.push(redirectUrl);
                }, 2000);
            } else {
                throw new Error(response.message || 'Failed to save phone number');
            }
        } catch (err) {
            console.error('Phone setup error:', err);
            const errorMessage = err.response?.data?.message || err.message || 'Failed to save phone number. Please try again.';
            setSaveStatus('error');
            setError(errorMessage);
        } finally {
            setIsLoading(false);
            setTimeout(() => setSaveStatus(null), 3000);
        }
    };

    const handleCancel = () => {
        // Reset validation state
        setValidationError(null);
        setIsTouched(false);
        setError(null);
        setSaveStatus(null);

        const redirectUrl = getRedirectUrl();
        router.push(redirectUrl);
    };

    // Show loading state while fetching user data
    if (isLoadingUserData) {
        return (
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="account_setup_phone_number">
                    <SidebarHeading title="Loading..." />
                    <div className="mb-3 mb-lg-4">
                        <div className="common_blackcard account_card">
                            <div className="common_blackcard_innerheader">
                                <div className="common_blackcard_innerheader_content">
                                    <div className="account_header_main">
                                        <h6>Phone Number</h6>
                                    </div>
                                    <p>Loading your phone number information...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </AccountLayout>
        );
    }

    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="account_setup_phone_number">
                    <SidebarHeading title={isUpdating ? "Update Phone Number" : "New Phone Number"} />
                    <div className="mb-3 mb-lg-4">
                        <div className="common_blackcard account_card">
                            <div className="common_blackcard_innerheader">
                                <div className="common_blackcard_innerheader_content">
                                    <div className="account_header_main justify-content-start">
                                        <div>
                                            <h6>Phone Number</h6>
                                        </div>
                                        <div className="account_status_indicator">
                                            <StatusIndicator
                                                saveStatus={saveStatus}
                                                error={error}
                                                successText="Saved"
                                                defaultText="Not saved"
                                            />
                                        </div>
                                    </div>
                                    <p>Provide your number to receive occasional updates, exclusive offers, or important notifications. We will never share your number without consent.</p>
                                </div>
                            </div>
                            <div className="common_blackcard_innerbody">
                                <div className="account_card_list">
                                    <Row>
                                        <Col lg={6}>
                                            <div className="account_card_list_item">
                                                <label>Phone Number</label>
                                                <TextInput
                                                    type="tel"
                                                    placeholder="Enter your phone number"
                                                    value={phoneNumber}
                                                    onChange={handlePhoneChange}
                                                    disabled={isLoading}
                                                />
                                                <small className="form-text text-muted">
                                                    {/* Validation errors take priority over save errors */}
                                                    {validationError && isTouched && (
                                                        <div className="mt-1">
                                                            <p style={{ color: 'red', fontSize: '14px' }}>
                                                                {validationError}
                                                            </p>
                                                        </div>
                                                    )}

                                                    {/* Show save errors only when no validation errors */}
                                                    {!validationError && error && saveStatus !== 'loading' && (
                                                        <div className="mt-1">
                                                            <p style={{ color: 'red', fontSize: '14px' }}>
                                                                {error}
                                                            </p>
                                                        </div>
                                                    )}
                                                </small>
                                            </div>
                                        </Col>
                                    </Row>


                                </div>
                            </div>
                        </div>
                        <div className="account_card_list_btns mt-3">
                            <button
                                className="btn-style white-btn"
                                onClick={handleCancel}
                                disabled={isLoading || isLoadingUserData}
                            >
                                Cancel
                            </button>
                            <button
                                className="btn-style"
                                onClick={handleSave}
                                disabled={isLoading || isLoadingUserData || !phoneNumber.trim() || (isTouched && validationError)}
                            >
                                {isLoading ? "Saving..." : isUpdating ? "Update Phone Number" : "Save Phone Number"}
                            </button>
                        </div>
                    </div>

                </div>
            </AccountLayout>
        </>
    )
}
